package nl.teqplay.vesselvoyagecompletenesstest.service.startup

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import nl.teqplay.vesselvoyagecompletenesstest.properties.VesselVoyageBackfillProperties
import nl.teqplay.vesselvoyagecompletenesstest.service.VesselVoyageBackfillService
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.context.event.ApplicationReadyEvent

class VesselVoyageBackfillStartupTest {

    private lateinit var backfillService: VesselVoyageBackfillService
    private lateinit var backfillProperties: VesselVoyageBackfillProperties
    private lateinit var backfillStartup: VesselVoyageBackfillStartup
    private lateinit var applicationReadyEvent: ApplicationReadyEvent

    @BeforeEach
    fun setUp() {
        backfillService = mockk()
        applicationReadyEvent = mockk()
        backfillStartup = VesselVoyageBackfillStartup(backfillService, backfillProperties)
    }

    @Test
    fun `should not run backfill when disabled`() {
        // Given
        backfillProperties = VesselVoyageBackfillProperties(enabled = false)
        backfillStartup = VesselVoyageBackfillStartup(backfillService, backfillProperties)

        // When
        backfillStartup.onApplicationReady()

        // Then
        verify(exactly = 0) { backfillService.performBackfill() }
    }

    @Test
    fun `should run backfill when enabled`() {
        // Given
        backfillProperties = VesselVoyageBackfillProperties(enabled = true)
        backfillStartup = VesselVoyageBackfillStartup(backfillService, backfillProperties)
        
        val result = VesselVoyageBackfillService.BackfillResult(
            visitsProcessed = 10,
            sofsProcessed = 5,
            totalProcessingTimeMs = 1000,
            errors = emptyList()
        )
        every { backfillService.performBackfill() } returns result

        // When
        backfillStartup.onApplicationReady()

        // Give async operation time to complete
        Thread.sleep(100)

        // Then
        verify(exactly = 1) { backfillService.performBackfill() }
    }

    @Test
    fun `should handle backfill service exceptions gracefully`() {
        // Given
        backfillProperties = VesselVoyageBackfillProperties(enabled = true)
        backfillStartup = VesselVoyageBackfillStartup(backfillService, backfillProperties)
        
        every { backfillService.performBackfill() } throws RuntimeException("Service error")

        // When
        backfillStartup.onApplicationReady()

        // Give async operation time to complete
        Thread.sleep(100)

        // Then - should not throw exception and should have called the service
        verify(exactly = 1) { backfillService.performBackfill() }
    }
}
