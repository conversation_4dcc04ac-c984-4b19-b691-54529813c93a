package nl.teqplay.vesselvoyagecompletenesstest.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PtoStatementOfFactsView
import nl.teqplay.vesselvoyage.client.VesselVoyageClient
import nl.teqplay.vesselvoyagecompletenesstest.properties.VesselVoyageBackfillProperties
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class VesselVoyageBackfillServiceTest {

    private lateinit var vesselVoyageClient: VesselVoyageClient
    private lateinit var entryChangeHandler: EntryChangeHandler
    private lateinit var sofChangeHandler: SOFChangeHandler
    private lateinit var backfillProperties: VesselVoyageBackfillProperties
    private lateinit var backfillService: VesselVoyageBackfillService

    @BeforeEach
    fun setUp() {
        vesselVoyageClient = mockk()
        entryChangeHandler = mockk(relaxed = true)
        sofChangeHandler = mockk(relaxed = true)
        backfillProperties = VesselVoyageBackfillProperties(
            enabled = true,
            api = VesselVoyageBackfillProperties.ApiProperties(
                retryAttempts = 2,
                retryDelayMs = 10
            ),
            pagination = VesselVoyageBackfillProperties.PaginationProperties(
                pageSize = 2,
                maxPages = 3
            )
        )
        backfillService = VesselVoyageBackfillService(
            vesselVoyageClient,
            entryChangeHandler,
            sofChangeHandler,
            backfillProperties
        )
    }

    @Test
    fun `should successfully backfill visits and SOFs`() {
        // Given
        val visit1 = mockk<Visit> { every { entryId } returns "visit1" }
        val visit2 = mockk<Visit> { every { entryId } returns "visit2" }
        val sof1 = mockk<PtoStatementOfFactsView> { every { entryId } returns "sof1" }
        val sof2 = mockk<PtoStatementOfFactsView> { every { entryId } returns "sof2" }

        val visitsBuilder = mockk<Any>()
        val sofsBuilder = mockk<Any>()

        every { vesselVoyageClient.visits() } returns visitsBuilder
        every { visitsBuilder.page(0) } returns visitsBuilder
        every { visitsBuilder.page(1) } returns visitsBuilder
        every { visitsBuilder.size(2) } returns visitsBuilder
        every { visitsBuilder.get() } returns listOf(visit1, visit2) andThen emptyList()

        every { vesselVoyageClient.statementOfFactsViews() } returns sofsBuilder
        every { sofsBuilder.page(0) } returns sofsBuilder
        every { sofsBuilder.page(1) } returns sofsBuilder
        every { sofsBuilder.size(2) } returns sofsBuilder
        every { sofsBuilder.get() } returns listOf(sof1, sof2) andThen emptyList()

        // When
        val result = backfillService.performBackfill()

        // Then
        assertEquals(2, result.visitsProcessed)
        assertEquals(2, result.sofsProcessed)
        assertTrue(result.errors.isEmpty())
        verify(exactly = 2) { entryChangeHandler.process(any()) }
        verify(exactly = 2) { sofChangeHandler.process(any()) }
    }

    @Test
    fun `should handle individual item processing errors gracefully`() {
        // Given
        val visit1 = mockk<Visit> { every { entryId } returns "visit1" }
        val visit2 = mockk<Visit> { every { entryId } returns "visit2" }

        val visitsBuilder = mockk<Any>()
        val sofsBuilder = mockk<Any>()

        every { vesselVoyageClient.visits() } returns visitsBuilder
        every { visitsBuilder.page(any()) } returns visitsBuilder
        every { visitsBuilder.size(any()) } returns visitsBuilder
        every { visitsBuilder.get() } returns listOf(visit1, visit2) andThen emptyList()

        every { vesselVoyageClient.statementOfFactsViews() } returns sofsBuilder
        every { sofsBuilder.page(any()) } returns sofsBuilder
        every { sofsBuilder.size(any()) } returns sofsBuilder
        every { sofsBuilder.get() } returns emptyList()

        every { entryChangeHandler.process(match { it.value == visit1 }) } throws RuntimeException("Processing error")
        every { entryChangeHandler.process(match { it.value == visit2 }) } returns Unit

        // When
        val result = backfillService.performBackfill()

        // Then
        assertEquals(1, result.visitsProcessed) // Only visit2 processed successfully
        assertEquals(0, result.sofsProcessed)
        assertEquals(1, result.errors.size)
        assertTrue(result.errors[0].contains("Failed to process visit visit1"))
    }

    @Test
    fun `should respect pagination limits`() {
        // Given
        val visit = mockk<Visit> { every { entryId } returns "visit1" }
        
        val visitsBuilder = mockk<Any>()
        val sofsBuilder = mockk<Any>()

        // Return data for all pages to test the limit
        every { vesselVoyageClient.visits() } returns visitsBuilder
        every { visitsBuilder.page(any()) } returns visitsBuilder
        every { visitsBuilder.size(any()) } returns visitsBuilder
        every { visitsBuilder.get() } returns listOf(visit)

        every { vesselVoyageClient.statementOfFactsViews() } returns sofsBuilder
        every { sofsBuilder.page(any()) } returns sofsBuilder
        every { sofsBuilder.size(any()) } returns sofsBuilder
        every { sofsBuilder.get() } returns emptyList()

        // When
        val result = backfillService.performBackfill()

        // Then
        // Should stop at maxPages (3) even though there's more data
        assertEquals(6, result.visitsProcessed) // 3 pages * 2 items per page
        verify(exactly = 3) { visitsBuilder.get() }
    }

    @Test
    fun `should retry failed API calls`() {
        // Given
        val visitsBuilder = mockk<Any>()
        val sofsBuilder = mockk<Any>()

        every { vesselVoyageClient.visits() } returns visitsBuilder
        every { visitsBuilder.page(any()) } returns visitsBuilder
        every { visitsBuilder.size(any()) } returns visitsBuilder
        every { visitsBuilder.get() } throws RuntimeException("Network error") andThen emptyList()

        every { vesselVoyageClient.statementOfFactsViews() } returns sofsBuilder
        every { sofsBuilder.page(any()) } returns sofsBuilder
        every { sofsBuilder.size(any()) } returns sofsBuilder
        every { sofsBuilder.get() } returns emptyList()

        // When
        val result = backfillService.performBackfill()

        // Then
        assertEquals(0, result.visitsProcessed)
        assertEquals(0, result.sofsProcessed)
        assertTrue(result.errors.isEmpty()) // Should succeed after retry
        verify(exactly = 2) { visitsBuilder.get() } // Initial call + 1 retry
    }

    @Test
    fun `should fail after exhausting retries`() {
        // Given
        val visitsBuilder = mockk<Any>()
        val sofsBuilder = mockk<Any>()

        every { vesselVoyageClient.visits() } returns visitsBuilder
        every { visitsBuilder.page(any()) } returns visitsBuilder
        every { visitsBuilder.size(any()) } returns visitsBuilder
        every { visitsBuilder.get() } throws RuntimeException("Persistent network error")

        every { vesselVoyageClient.statementOfFactsViews() } returns sofsBuilder
        every { sofsBuilder.page(any()) } returns sofsBuilder
        every { sofsBuilder.size(any()) } returns sofsBuilder
        every { sofsBuilder.get() } returns emptyList()

        // When
        val result = backfillService.performBackfill()

        // Then
        assertEquals(0, result.visitsProcessed)
        assertEquals(1, result.errors.size)
        assertTrue(result.errors[0].contains("Failed to fetch visits page 1"))
        verify(exactly = 2) { visitsBuilder.get() } // Initial + retries
    }
}
