package nl.teqplay.vesselvoyagecompletenesstest

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.common.BaseTest
import org.bson.conversions.Bson
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary

class ApplicationTest : BaseTest() {
    @Test
    fun contextLoads() {
        // This test verifies that the Spring context loads successfully with all configurations
    }
}

@TestConfiguration
@Import(Application::class)
class ApplicationTestConfig {
    @Bean
    @Primary
    fun mongoDatabase() = getMockMongoDB()
}

private fun <T : Any> getMockIterable() = mock<FindIterable<T>>()

fun getMockMongoDB(): MongoDatabase {
    return mock {
        val mockCollection = mock<MongoCollection<Any>> {
            val iterable = getMockIterable<Any>()
            on { createIndex(any<Bson>(), any()) } doReturn ""
            on { find() } doReturn iterable
            on { find(any<Bson>()) } doReturn iterable
        }
        on { getCollection(any(), any<Class<Any>>()) } doReturn mockCollection
        val iterable = getMockIterable<String>()
        on { listCollectionNames() } doReturn iterable
    }
}
