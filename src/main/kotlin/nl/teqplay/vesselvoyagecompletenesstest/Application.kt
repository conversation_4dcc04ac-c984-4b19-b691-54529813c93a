package nl.teqplay.vesselvoyagecompletenesstest

import nl.teqplay.skeleton.actuator.AutoTimeoutHealthIndicator
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(
    exclude = [
        // Spring boot auto configurations as they include health checks we don't want to use
        MongoAutoConfiguration::class,
        RabbitAutoConfiguration::class,

        // Skeleton health configurations
        AutoTimeoutHealthIndicator::class
    ],
)
@ConfigurationPropertiesScan
@EnableScheduling
class Application : SpringBootServletInitializer()

fun main(args: Array<String>) {
    runApplication<Application>(*args)
}
