package nl.teqplay.vesselvoyagecompletenesstest.model

import com.fasterxml.jackson.annotation.JsonProperty
import org.bson.codecs.pojo.annotations.BsonId

data class VisitCompleteness(
    @BsonId
    val _id: String,

    @JsonProperty("startEnd")
    val startEndTimestampPresent: <PERSON><PERSON><PERSON>,
    @JsonProperty("mainStartEnd")
    val mainPortAreaStartEndTimestampPresent: <PERSON><PERSON><PERSON>,
    @JsonProperty("subStartEnd")
    val subPortAreaStartEndTimestampPresent: <PERSON><PERSON><PERSON>,
)
