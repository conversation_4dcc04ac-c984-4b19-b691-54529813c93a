package nl.teqplay.vesselvoyagecompletenesstest.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness
import org.springframework.stereotype.Repository

@Repository
class SOFCompletenessDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<SOFCompleteness>("sofCompleteness")

    fun save(sof: SOFCompleteness) {
        collection.save(sof)
    }

    fun delete(id: String) {
        collection.deleteOneById(id)
    }

    fun list(): List<SOFCompleteness> {
        return collection.find().toList()
    }
}
