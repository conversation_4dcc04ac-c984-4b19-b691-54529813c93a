package nl.teqplay.vesselvoyagecompletenesstest.config

import nl.teqplay.skeleton.rabbitmq.RabbitMqEventHandler
import nl.teqplay.vesselvoyagecompletenesstest.properties.VesselVoyageQueueProperties
import nl.teqplay.vesselvoyagecompletenesstest.service.queue.VesselVoyageChangeMessageHandler
import org.springframework.amqp.rabbit.annotation.EnableRabbit
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import java.lang.annotation.Inherited

@Configuration
@EnableRabbit
class AmqpConfiguration {
    @Bean(VESSELVOYAGE_CHANGE_AMQP)
    fun rabbitMqEventHandlerVesselVoyageEvents(queue: VesselVoyageQueueProperties) =
        RabbitMqEventHandler(queue.qos, queue.uri)
}

const val VESSELVOYAGE_CHANGE_AMQP = "vesselVoyageChange"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(VESSELVOYAGE_CHANGE_AMQP)
annotation class VesselVoyageChangeAMQP

/** Ensures that the event consumption would start only after application is initialized. */
@Component
class RabbitMqInitializer(
    private val queue: VesselVoyageQueueProperties,
    @VesselVoyageChangeAMQP private val vesselVoyageChangeHandler: RabbitMqEventHandler,
    private val vesselVoyageChangeMessageHandler: VesselVoyageChangeMessageHandler,
) {
    @EventListener(ApplicationReadyEvent::class)
    fun initQueues() {
        vesselVoyageChangeHandler.createChannelAndListen(
            vesselVoyageChangeMessageHandler,
            queue.queue,
            queue.consume
        )
    }

    @EventListener(ContextClosedEvent::class)
    fun closeQueues() {
        vesselVoyageChangeHandler.closeChannel()
    }
}
