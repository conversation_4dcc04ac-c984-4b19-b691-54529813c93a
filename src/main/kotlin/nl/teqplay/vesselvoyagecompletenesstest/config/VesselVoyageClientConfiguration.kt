package nl.teqplay.vesselvoyagecompletenesstest.config

import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientCredentialsProvider
import nl.teqplay.vesselvoyage.client.VesselVoyageClient
import nl.teqplay.vesselvoyagecompletenesstest.properties.VesselVoyageBackfillProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

@Configuration
class VesselVoyageClientConfiguration {

    @Bean
    fun vesselVoyageClient(
        backfillProperties: VesselVoyageBackfillProperties,
        credentialsProvider: KeycloakS2SClientCredentialsProvider
    ): VesselVoyageClient {
        return VesselVoyageClient.builder()
            .baseUrl(backfillProperties.api.baseUrl)
            .timeout(Duration.ofMillis(backfillProperties.api.timeout))
            .credentialsProvider(credentialsProvider)
            .build()
    }
}
