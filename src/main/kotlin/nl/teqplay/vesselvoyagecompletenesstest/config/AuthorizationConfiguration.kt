package nl.teqplay.vesselvoyagecompletenesstest.config

import nl.teqplay.skeleton.auth.credentials.AUTH_PATHS
import nl.teqplay.skeleton.auth.credentials.AuthorizationConfigurer
import nl.teqplay.skeleton.auth.credentials.SWAGGER_PATHS
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class AuthorizationConfiguration {
    @Bean
    fun authorizationConfigurer(): AuthorizationConfigurer =
        AuthorizationConfigurer(
            AUTH_PATHS + SWAGGER_PATHS + listOf(
                "/actuator/health",
                "/actuator/prometheus",
                "/actuator/atlas",
            )
        )
}
