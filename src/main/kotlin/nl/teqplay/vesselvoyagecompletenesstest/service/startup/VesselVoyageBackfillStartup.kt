package nl.teqplay.vesselvoyagecompletenesstest.service.startup

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyagecompletenesstest.properties.VesselVoyageBackfillProperties
import nl.teqplay.vesselvoyagecompletenesstest.service.VesselVoyageBackfillService
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class VesselVoyageBackfillStartup(
    private val backfillService: VesselVoyageBackfillService,
    private val backfillProperties: VesselVoyageBackfillProperties
) {
    private val log = KotlinLogging.logger {}

    /**
     * Executes the backfill process after application startup.
     * Uses @Order to ensure this runs after other startup processes like RabbitMQ initialization.
     */
    @EventListener(ApplicationReadyEvent::class)
    @Order(1000) // Run after other startup processes
    fun onApplicationReady() {
        if (!backfillProperties.enabled) {
            log.info { "VesselVoyage backfill is disabled (backfill.vesselvoyage.enabled=false)" }
            return
        }

        log.info { "Application ready - starting VesselVoyage backfill process" }
        
        // Run backfill asynchronously to avoid blocking application startup
        CompletableFuture.runAsync {
            try {
                val result = backfillService.performBackfill()
                
                if (result.errors.isEmpty()) {
                    log.info { 
                        "Backfill completed successfully: " +
                        "visits=${result.visitsProcessed}, SOFs=${result.sofsProcessed}, " +
                        "time=${result.totalProcessingTimeMs / 1000}s"
                    }
                } else {
                    log.warn { 
                        "Backfill completed with ${result.errors.size} errors: " +
                        "visits=${result.visitsProcessed}, SOFs=${result.sofsProcessed}, " +
                        "time=${result.totalProcessingTimeMs / 1000}s"
                    }
                    
                    // Log first few errors for visibility
                    result.errors.take(5).forEach { error ->
                        log.warn { "Backfill error: $error" }
                    }
                    
                    if (result.errors.size > 5) {
                        log.warn { "... and ${result.errors.size - 5} more errors" }
                    }
                }
            } catch (e: Exception) {
                log.error(e) { "Fatal error during backfill startup process" }
            }
        }.exceptionally { throwable ->
            log.error(throwable) { "Unexpected error in backfill async execution" }
            null
        }
    }
}
