package nl.teqplay.vesselvoyagecompletenesstest.service.queue

import com.fasterxml.jackson.core.JacksonException
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.skeleton.rabbitmq.BaseMessageHandler
import nl.teqplay.vesselvoyage.apiv2.model.EntryIdentifier
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingEntryChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingSofChange
import nl.teqplay.vesselvoyagecompletenesstest.service.EntryChangeHandler
import nl.teqplay.vesselvoyagecompletenesstest.service.SOFChangeHandler
import org.springframework.stereotype.Service

@Service
class VesselVoyageChangeMessageHandler(
    private val sofChangeHandler: SOFC<PERSON>eH<PERSON><PERSON>,
    private val entryChangeHandler: EntryChangeHand<PERSON>,
    private val objectMapper: ObjectMapper
) : BaseMessageHandler() {

    val log = KotlinLogging.logger {}

    override fun accept(payload: String) {
        try {
            val change: OutgoingChange<out EntryIdentifier> =
                objectMapper.readValue(payload, object : TypeReference<OutgoingChange<out EntryIdentifier>>() {})

            when (change) {
                is OutgoingEntryChange -> {
                    log.debug { "Received Entry change: ${change.value}" }
                    entryChangeHandler.process(change)
                }
                is OutgoingSofChange -> {
                    log.debug { "Received SOF change: ${change.value}" }
                    sofChangeHandler.process(change)
                }
            }
        } catch (e: JacksonException) {
            log.error(e) { "Cannot deserialize VesselVoyage change, dropping it.\n$payload" }
            return
        } catch (e: Throwable) {
            log.error(e) { "Error processing VesselVoyage message, dropping it.\n$payload" }
        }
    }
}
