package nl.teqplay.vesselvoyagecompletenesstest.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.vesselvoyagecompletenesstest.datasource.SOFCompletenessDataSource
import nl.teqplay.vesselvoyagecompletenesstest.datasource.VisitCompletenessDataSource
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.util.concurrent.atomic.AtomicLong
import kotlin.system.measureTimeMillis

@Service
class MetricService(
    meterRegistry: MeterRegistry,
    private val visitCompletenessDataSource: VisitCompletenessDataSource,
    private val sofCompletenessDataSource: SOFCompletenessDataSource
) {
    private val totalVisitsGauge = AtomicLong(0)
    private val completeVisitsGauge = AtomicLong(0)
    private val completeVisitStartEndTimeGauge = AtomicLong(0)
    private val completeVisitMainPortStartEndGauge = AtomicLong(0)
    private val completeVisitSubPortStartEndGauge = AtomicLong(0)

    private val totalSOFsGauge = AtomicLong(0)
    private val completeSOFsGauge = AtomicLong(0)
    private val completeSOFPilotInboundGauge = AtomicLong(0)
    private val completeSOFPilotOutboundGauge = AtomicLong(0)
    private val completeSOFAnyTugPresentGauge = AtomicLong(0)

    private val log = KotlinLogging.logger {}

    // Register gauges with the meter registry
    init {
        meterRegistry.gauge("visits_total", totalVisitsGauge)
        meterRegistry.gauge("visits_complete_total", completeVisitsGauge)
        meterRegistry.gauge("complete_visit_start_end_time_total", completeVisitStartEndTimeGauge)
        meterRegistry.gauge("complete_visit_main_port_start_end_total", completeVisitMainPortStartEndGauge)
        meterRegistry.gauge("complete_visit_sub_port_start_end_total", completeVisitSubPortStartEndGauge)

        meterRegistry.gauge("sofs_total", totalSOFsGauge)
        meterRegistry.gauge("sofs_complete_total", completeSOFsGauge)
        meterRegistry.gauge("complete_sof_pilot_inbound_total", completeSOFPilotInboundGauge)
        meterRegistry.gauge("complete_sof_pilot_outbound_total", completeSOFPilotOutboundGauge)
        meterRegistry.gauge("complete_sof_any_tug_present_total", completeSOFAnyTugPresentGauge)
    }

    /**
     * Recalculates all metrics based on current database state
     * Runs every hour at the top of the hour
     */
    @Scheduled(cron = "0 */5 * * * *")
    fun recalculateMetrics() {
        log.info { "Recalculating metrics" }
        val time = measureTimeMillis {
            recalculateVisitMetrics()
            recalculateSOFMetrics()
        }

        log.info { "Recalculated metrics in ${ time / 1000 } seconds" }
    }

    /**
     * Recalculates visit metrics from database
     */
    private fun recalculateVisitMetrics() {
        val visits = visitCompletenessDataSource.list()

        // Reset all visit gauges
        totalVisitsGauge.set(visits.size.toLong())

        // Count complete fields
        val completeStartEnd = visits.count { it.startEndTimestampPresent }
        val completeMainPort = visits.count { it.mainPortAreaStartEndTimestampPresent }
        val completeSubPort = visits.count { it.subPortAreaStartEndTimestampPresent }
        val completeVisits = visits.count {
            it.startEndTimestampPresent &&
                it.mainPortAreaStartEndTimestampPresent &&
                it.subPortAreaStartEndTimestampPresent
        }

        // Update complete gauges
        completeVisitStartEndTimeGauge.set(completeStartEnd.toLong())
        completeVisitMainPortStartEndGauge.set(completeMainPort.toLong())
        completeVisitSubPortStartEndGauge.set(completeSubPort.toLong())
        completeVisitsGauge.set(completeVisits.toLong())
    }

    /**
     * Recalculates SOF metrics from database
     */
    private fun recalculateSOFMetrics() {
        val sofs = sofCompletenessDataSource.list()

        // Reset all SOF gauges
        totalSOFsGauge.set(sofs.size.toLong())

        // Count complete fields
        val completePilotInbound = sofs.count { it.pilotInboundPresent }
        val completePilotOutbound = sofs.count { it.pilotOutboundPresent }
        val completeAnyTug = sofs.count { it.anyTugPresent }
        val completeSOFs = sofs.count {
            it.pilotInboundPresent &&
                it.pilotOutboundPresent &&
                it.anyTugPresent
        }

        // Update complete gauges
        completeSOFPilotInboundGauge.set(completePilotInbound.toLong())
        completeSOFPilotOutboundGauge.set(completePilotOutbound.toLong())
        completeSOFAnyTugPresentGauge.set(completeAnyTug.toLong())
        completeSOFsGauge.set(completeSOFs.toLong())
    }
}
