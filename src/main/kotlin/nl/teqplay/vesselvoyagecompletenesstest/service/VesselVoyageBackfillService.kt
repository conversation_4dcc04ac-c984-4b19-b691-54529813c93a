package nl.teqplay.vesselvoyagecompletenesstest.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingEntryChange
import nl.teqplay.vesselvoyage.apiv2.model.OutgoingSofChange
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PtoStatementOfFactsView
import nl.teqplay.vesselvoyage.client.VesselVoyageClient
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyagecompletenesstest.properties.VesselVoyageBackfillProperties
import org.springframework.stereotype.Service
import kotlin.system.measureTimeMillis

@Service
class VesselVoyageBackfillService(
    private val vesselVoyageClient: VesselVoyageClient,
    private val entryChangeHandler: EntryChangeHandler,
    private val sofChangeHandler: SOFChange<PERSON><PERSON><PERSON>,
    private val backfillProperties: VesselVoyageBackfillProperties
) {
    private val log = KotlinLogging.logger {}

    data class BackfillResult(
        val visitsProcessed: Int,
        val sofsProcessed: Int,
        val totalProcessingTimeMs: Long,
        val errors: List<String>
    )

    fun performBackfill(): BackfillResult {
        log.info { "Starting VesselVoyage completeness backfill process" }
        val errors = mutableListOf<String>()
        var visitsProcessed = 0
        var sofsProcessed = 0

        val totalTime = measureTimeMillis {
            try {
                // Backfill visits
                log.info { "Starting visits backfill" }
                val visitTime = measureTimeMillis {
                    visitsProcessed = backfillVisits(errors)
                }
                log.info { "Completed visits backfill: $visitsProcessed visits processed in ${visitTime / 1000}s" }

                // Backfill SOFs
                log.info { "Starting SOFs backfill" }
                val sofTime = measureTimeMillis {
                    sofsProcessed = backfillSOFs(errors)
                }
                log.info { "Completed SOFs backfill: $sofsProcessed SOFs processed in ${sofTime / 1000}s" }

            } catch (e: Exception) {
                val errorMsg = "Critical error during backfill process: ${e.message}"
                log.error(e) { errorMsg }
                errors.add(errorMsg)
            }
        }

        val result = BackfillResult(visitsProcessed, sofsProcessed, totalTime, errors)
        log.info { 
            "Backfill process completed: " +
            "visits=$visitsProcessed, SOFs=$sofsProcessed, " +
            "total_time=${totalTime / 1000}s, errors=${errors.size}"
        }

        return result
    }

    private fun backfillVisits(errors: MutableList<String>): Int {
        var processedCount = 0
        var currentPage = 0
        var hasMorePages = true

        while (hasMorePages && currentPage < backfillProperties.pagination.maxPages) {
            try {
                log.debug { "Fetching visits page ${currentPage + 1}" }
                
                val visits = retryOperation {
                    vesselVoyageClient.visits()
                        .page(currentPage)
                        .size(backfillProperties.pagination.pageSize)
                        .get()
                }

                if (visits.isEmpty()) {
                    hasMorePages = false
                    log.debug { "No more visits found, stopping pagination" }
                } else {
                    log.info { "Processing page ${currentPage + 1}: ${visits.size} visits" }
                    
                    visits.forEach { visit ->
                        try {
                            val entryChange = OutgoingEntryChange(
                                value = visit,
                                action = Action.CREATE
                            )
                            entryChangeHandler.process(entryChange)
                            processedCount++
                        } catch (e: Exception) {
                            val errorMsg = "Failed to process visit ${visit.entryId}: ${e.message}"
                            log.warn(e) { errorMsg }
                            errors.add(errorMsg)
                        }
                    }
                    
                    currentPage++
                    
                    // Log progress every 10 pages
                    if (currentPage % 10 == 0) {
                        log.info { "Progress: processed $processedCount visits across $currentPage pages" }
                    }
                }
            } catch (e: Exception) {
                val errorMsg = "Failed to fetch visits page ${currentPage + 1}: ${e.message}"
                log.error(e) { errorMsg }
                errors.add(errorMsg)
                break
            }
        }

        if (currentPage >= backfillProperties.pagination.maxPages) {
            log.warn { "Reached maximum page limit (${backfillProperties.pagination.maxPages}) for visits" }
        }

        return processedCount
    }

    private fun backfillSOFs(errors: MutableList<String>): Int {
        var processedCount = 0
        var currentPage = 0
        var hasMorePages = true

        while (hasMorePages && currentPage < backfillProperties.pagination.maxPages) {
            try {
                log.debug { "Fetching SOFs page ${currentPage + 1}" }
                
                val sofs = retryOperation {
                    vesselVoyageClient.statementOfFactsViews()
                        .page(currentPage)
                        .size(backfillProperties.pagination.pageSize)
                        .get()
                }

                if (sofs.isEmpty()) {
                    hasMorePages = false
                    log.debug { "No more SOFs found, stopping pagination" }
                } else {
                    log.info { "Processing page ${currentPage + 1}: ${sofs.size} SOFs" }
                    
                    sofs.forEach { sof ->
                        try {
                            val sofChange = OutgoingSofChange(
                                value = sof,
                                action = Action.CREATE
                            )
                            sofChangeHandler.process(sofChange)
                            processedCount++
                        } catch (e: Exception) {
                            val errorMsg = "Failed to process SOF ${sof.entryId}: ${e.message}"
                            log.warn(e) { errorMsg }
                            errors.add(errorMsg)
                        }
                    }
                    
                    currentPage++
                    
                    // Log progress every 10 pages
                    if (currentPage % 10 == 0) {
                        log.info { "Progress: processed $processedCount SOFs across $currentPage pages" }
                    }
                }
            } catch (e: Exception) {
                val errorMsg = "Failed to fetch SOFs page ${currentPage + 1}: ${e.message}"
                log.error(e) { errorMsg }
                errors.add(errorMsg)
                break
            }
        }

        if (currentPage >= backfillProperties.pagination.maxPages) {
            log.warn { "Reached maximum page limit (${backfillProperties.pagination.maxPages}) for SOFs" }
        }

        return processedCount
    }

    private fun <T> retryOperation(operation: () -> T): T {
        var lastException: Exception? = null
        
        repeat(backfillProperties.api.retryAttempts) { attempt ->
            try {
                return operation()
            } catch (e: Exception) {
                lastException = e
                if (attempt < backfillProperties.api.retryAttempts - 1) {
                    log.warn { "Attempt ${attempt + 1} failed, retrying in ${backfillProperties.api.retryDelayMs}ms: ${e.message}" }
                    Thread.sleep(backfillProperties.api.retryDelayMs)
                }
            }
        }
        
        throw lastException ?: RuntimeException("Operation failed after ${backfillProperties.api.retryAttempts} attempts")
    }
}
