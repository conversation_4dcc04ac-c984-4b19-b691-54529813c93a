package nl.teqplay.vesselvoyagecompletenesstest.properties

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "backfill.vesselvoyage")
data class VesselVoyageBackfillProperties(
    val enabled: Boolean = false,
    val api: ApiProperties = ApiProperties(),
    val pagination: PaginationProperties = PaginationProperties()
) {
    data class ApiProperties(
        val baseUrl: String = "https://api.vesselvoyage.dev.teqplay.com",
        val timeout: Long = 30000, // 30 seconds
        val retryAttempts: Int = 3,
        val retryDelayMs: Long = 1000
    )

    data class PaginationProperties(
        val pageSize: Int = 100,
        val maxPages: Int = 1000 // Safety limit to prevent infinite loops
    )
}
