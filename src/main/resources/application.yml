mongodb:
  host: localhost
  port: 27017
  authDb: admin
  username:
  password:
  db: vesselvoyage

# when receiving the SIGTERM signal stop accepting new connections and finish process existing connections
server:
  shutdown: graceful

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
  endpoint:
    prometheus:
      enabled: true

queue:
  vesselvoyage:
    uri: amqps://VesselVoyageDevTesting:<EMAIL>:5671/VesselVoyageDev
    queue: VesselVoyageV2Dev-Testing
    qos: 10
    consume: true
